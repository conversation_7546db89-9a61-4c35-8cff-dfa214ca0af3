import 'package:go_router/go_router.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:towchain_service_provider/core/routes/routing_paths.dart';
import 'package:towchain_service_provider/presentation/screens/auth/view/business_sign_up_screen.dart';
import 'package:towchain_service_provider/presentation/screens/auth/view/select_account_type_screen.dart';
import 'package:towchain_service_provider/presentation/screens/auth/view/verify_otp_screen.dart';
import '../../presentation/screens/auth/view/enable_location_screen.dart';
import '../../presentation/screens/auth/view/forgot_password_screen.dart';
import '../../presentation/screens/auth/view/login_screen.dart';
import '../../presentation/screens/auth/view/on_board_screen.dart';
import '../../presentation/screens/auth/view/sign_up_screen.dart';
import '../../presentation/screens/auth/view/splash_screen.dart';
import '../../presentation/screens/home/<USER>';
import '../../presentation/screens/license/license_screen.dart';
import '../imports/core_imports.dart';

final rootNavigatorKey = GlobalKey<NavigatorState>();
final shellNavigatorKey = GlobalKey<NavigatorState>();

/// This will be replaced with a real auth provider

final goRouterProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    navigatorKey: rootNavigatorKey,
    initialLocation: '/splash',
    debugLogDiagnostics: true,
    routes: [
      /// Splash Screen
      GoRoute(path: Routes.splash, builder: (context, state) => SplashScreen()),

      GoRoute(
        path: Routes.onboard,
        builder: (context, state) => OnBoardScreen(),
      ),

      /// Login Screen
      GoRoute(
        path: Routes.login,
        builder: (context, state) => const LoginScreen(),
      ),

      /// SignUp Screen
      GoRoute(
        path: Routes.signup,
        builder: (context, state) => const SignupScreen(),
      ),
      GoRoute(
        path: Routes.verifyOtp,
        builder: (context, state) => const VerifyOtpScreen(),
      ),

      //
      /// Mobile Account Settings Screen
      GoRoute(
        path: Routes.forgotPassword,
        builder: (context, state) => const ForgotPasswordScreen(),
      ),

      /// Select Account Type Screen
      GoRoute(
        path: Routes.selectAccountType,
        builder: (context, state) => const SelectAccountTypeScreen(),
      ),
      //
      /// BusinessSignUpScreen Mobile Screen
      GoRoute(
        path: Routes.businessSignup,
        parentNavigatorKey: rootNavigatorKey,
        builder: (context, state) => const BusinessSignUpScreen(),
      ),

      /// Enable Location Screen
      GoRoute(
        path: Routes.enableLocation,
        builder: (context, state) => const EnableLocationScreen(),
      ),

      /// License Screen
      GoRoute(
        path: Routes.license,
        builder: (context, state) => const LicenseScreen(),
      ),

      /// Home Screen
      GoRoute(
        path: Routes.home,
        builder: (context, state) => const HomeScreen(),
      ),
      //
      // ...personalRoutes,
    ],
  );
});
